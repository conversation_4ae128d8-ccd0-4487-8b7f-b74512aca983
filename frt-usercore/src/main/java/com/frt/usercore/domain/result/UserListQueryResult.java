/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.result;

import com.frt.usercore.domain.entity.UserInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 员工列表查询结果
 *
 * <AUTHOR>
 * @version UserListQueryResult.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserListQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分页信息
     */
    private Pagination pagination;

    /**
     * 员工列表
     */
    private List<UserInfo> list;

    /**
     * 分页信息内部类
     */
    @Data
    public static class Pagination implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 当前页码
         */
        private Integer page;

        /**
         * 每页条数
         */
        private Integer pageSize;

        /**
         * 总条数
         */
        private Long totalCount;

        /**
         * 总页数
         */
        private Integer totalPage;
    }
}