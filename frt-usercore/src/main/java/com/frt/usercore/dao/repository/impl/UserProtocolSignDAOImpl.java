package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.UserProtocolSignDO;
import com.frt.usercore.dao.mapper.UserProtocolSignMapper;
import com.frt.usercore.dao.repository.UserProtocolSignDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户协议签署记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class UserProtocolSignDAOImpl extends ServiceImpl<UserProtocolSignMapper, UserProtocolSignDO> implements UserProtocolSignDAO {

}
