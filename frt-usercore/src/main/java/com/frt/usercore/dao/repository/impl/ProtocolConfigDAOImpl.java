package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.dao.mapper.ProtocolConfigMapper;
import com.frt.usercore.dao.repository.ProtocolConfigDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 协议配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class ProtocolConfigDAOImpl extends ServiceImpl<ProtocolConfigMapper, ProtocolConfigDO> implements ProtocolConfigDAO {

}
