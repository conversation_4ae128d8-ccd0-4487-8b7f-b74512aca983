/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.*;
import com.frt.usercore.domain.result.UserDetailQueryResult;
import com.frt.usercore.domain.result.UserListQueryResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.service.MerchantUserManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version UserManagerApi.java, v 0.1 2025-08-27 17:29 zhangling
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/user")
public class UserManagerApi {

    private final MerchantUserManagerService merchantUserManagerService;

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/query-user-list")
    UserListQueryResult getUserList(@RequestBody PageParam<UserListQueryParam> param) {
        return merchantUserManagerService.getUserList(param);
    }

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @PostMapping("/get-user-detail")
    UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param) {
        return merchantUserManagerService.getUserDetail(param);
    }

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/add-user")
    CommonResult addUser(@RequestBody UserAddParam param) {
        return merchantUserManagerService.addUser(param);
    }

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/update-user")
    CommonResult updateUser(@RequestBody UserUpdateParam param) {
        return merchantUserManagerService.updateUser(param);
    }

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/delete-user")
    CommonResult deleteUser(@RequestBody UserDeleteParam param) {
        return merchantUserManagerService.deleteUser(param);
    }
}